{"name": "snap-check", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@types/node": "^24.1.0", "lucide-react": "^0.536.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "vite": "^6.0.3"}}